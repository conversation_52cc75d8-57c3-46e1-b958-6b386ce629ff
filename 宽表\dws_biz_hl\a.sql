with tzccz_zcczxm as (
select
	distinct
    t1.bsn_prj_wrd,
    t1.bsn_prj_id,
    t1.project_code, -- 项目编号
    t1.project_name, -- 项目名称
    t1.lessor_nm, -- 出租方名称
    t1.rent_lst_prc, -- 挂牌价格（元）
    t1.prj_prc_unit_dsc, -- 挂牌价格单位
    t1.rent_lit_tot_prc, -- 租金挂牌总价(元)
    t1.plan_lease_area, -- 拟出租面积(平方米)
    t1.esr_strt_dt, -- 披露开始日期
    t1.rnt_fee_est_yuan, -- 租金估价(元)
    t1.esr_end_dt, -- 披露结束日期
    t1.prj_sts_dsc, -- 项目状态
    t31.usr_nm as prj_pnp, -- 项目负责人
    t26.org_nm as prj_blng_dept, -- 项目所属部门
    t27.org_nm as prj_clm_dept_id, -- 项目认领部门
    t1.pick_mod_dsc, -- 遴选方式
    t1.ntw_bid_tp_dsc, -- 网络竞价类型
    t1.deposit_yuan, -- 保证金金额(元)
    t1.ast_cgy_dsc, -- 资产类别
    t1.nbjcqkmc, -- 内部决策情况名称
    t1.project_type_dsc, -- 项目类型
    t1.authorize_unit, -- 批准单位
    t1.est_unit_nm, -- 估价单位名称
    t1.est_file_avl_dt_beg, -- 估价文件有效期起始
    t1.est_file_avl_dt_ct_of, -- 估价文件有效期截止
    t1.is_exst_prty_rentw, -- 是否存在优先承租权
    t1.rnt_fee_prj_prc_rmrk, -- 租金挂牌价备注
    t1.pcolt_lhder_cnt, -- 拟征集承租方个数
    t1.rent_postulate, -- 承租方资格条件
    t1.mbsh_org, -- 会员机构
    t1.deal_mth_cd_dsc, -- 成交方式
    t1.use_rqmt_cd_dsc, -- 房屋使用用途
    t1.is_nal_hs_rent -- 是否国有房屋出租
from dwd.dwd_ast_lease_prj_fct t1
LEFT JOIN  (
SELECT 
    id  --代码 
    ,usr_nm  --码值
FROM  std.std_bjhl_tuser_d  --用户管理
WHERE  dt='${dmp_day}') t31                          
ON t1.prj_pnp=t31.ID
INNER JOIN (
SELECT 
    id  ,--代码 
    org_nm  --码值
FROM std.std_bjhl_lborganization_d --组织机构
WHERE dt = '${dmp_day}' and org_nm LIKE '%市属%') t26
ON t1.prj_blng_dept = t26.id
LEFT JOIN (
SELECT 
    id  ,--代码 
    org_nm  --码值
FROM std.std_bjhl_lborganization_d --组织机构
WHERE dt = '${dmp_day}') t27
ON t1.prj_clm_dept_id = t27.id

where dt = '${dmp_day}' and t1.project_code is not NULL
),
tzccz_czfxx as (
SELECT 
    project_id,
    concat_ws(',', collect_set(cast(lessor_tp_dsc AS STRING))) AS lessor_tp_dsc, -- 出租方类型描述
    concat_ws(',', collect_set(cast(T2.ast_src_nm AS STRING))) AS ast_src_nm_2, -- 资产来源(2级)
    concat_ws(',', collect_set(cast(T3.ast_src_nm AS STRING))) AS ast_src_nm_3, -- 资产来源(3级)
    concat_ws(',', collect_set(cast(T4.ast_src_nm AS STRING))) AS ast_src_nm_4, -- 资产来源(4级)
    concat_ws(',', collect_set(cast(blng_grp AS STRING))) AS blng_grp,          -- 所属集团
    concat_ws(',', collect_set(cast(cty_contri_corp_lead_dept AS STRING))) AS cty_contri_corp_lead_dept, -- 国家出资企业或主管部门
    concat_ws(',', collect_set(cast(k1.lessor_nm AS STRING))) AS ast_src_entp_nm_1, -- 资产来源（企业1级）
    concat_ws(',', collect_set(cast(k2.lessor_nm AS STRING))) AS ast_src_entp_nm_2, -- 资产来源（企业2级）
    concat_ws(',', collect_set(cast(k3.lessor_nm AS STRING))) AS ast_src_entp_nm_3, -- 资产来源（企业3级）
    concat_ws(',', collect_set(cast(k4.lessor_nm AS STRING))) AS ast_src_entp_nm_4, -- 资产来源（企业4级）
    concat_ws(',', collect_set(cast(industry_tp_dsc AS STRING))) AS industry_tp_dsc, -- 所属行业类型描述
    concat_ws(',', collect_set(cast(ctc_psn AS STRING))) AS ctc_psn,                -- 联系人
    concat_ws(',', collect_set(cast(ctc_psn_phone AS STRING))) AS ctc_psn_phone,     -- 联系人手机号
    concat_ws(',', collect_set(cast(msg_addr AS STRING))) AS msg_addr,              -- 通讯地址
    concat_ws(',', collect_set(cast(contact_bank_account_nm AS STRING))) AS contact_bank_account_nm, -- 银行账户名称
    concat_ws(',', collect_set(cast(contact_bank_account_acc_no AS STRING))) AS contact_bank_account_acc_no, -- 银行账户账号
    concat_ws(',', collect_set(cast(opn_acc_bnk_nm AS STRING))) AS opn_acc_bnk_nm,   -- 开户银行名称
    concat_ws(',', collect_set(cast(opn_acc_bnk_subbr AS STRING))) AS opn_acc_bnk_subbr, -- 开户银行支行
    concat_ws(',', collect_set(cast(dep_bnk_bnk_cd AS STRING))) AS dep_bnk_bnk_cd,    -- 开户行联行号
    concat_ws(',', collect_set(cast(oasset_reg_org_dsc AS STRING))) AS oasset_reg_org_dsc,--国资监管机构描述
    concat_ws(',', collect_set(cast(custd_org_depdc_prov AS STRING))) AS custd_org_depdc_prov --监管机构属地(省)
FROM std.std_bjhl_tzccz_czfxx_d T1
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='1') T2 ON T2.ast_src_cd = T1.ast_src_2_cla
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='2') T3 ON T3.ast_src_cd = T1.ast_src_3_cla
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='3') T4 ON T4.ast_src_cd = T1.ast_src_4_cla
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k1 WHERE dt = '${dmp_day}'
) k1 ON T1.ast_src_entp_1_cla = k1.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k2 WHERE dt = '${dmp_day}'
) k2 ON T1.ast_src_entp_2_cla = k2.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k3 WHERE dt = '${dmp_day}'
) k3 ON T1.ast_src_entp_3_cla = k3.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k4 WHERE dt = '${dmp_day}'
) k4 ON T1.ast_src_entp_4_cla = k4.id
WHERE T1.dt = '${dmp_day}' AND T1.project_id IS NOT NULL
GROUP BY project_id
),
tzccz_cjjl as (
 select 
    replace(replace(bsn_prj_wrd,'BJHL',''),'ZCCZ','') as bsn_prj_wrd,      --业务项目关键字
    lease_tm_legth,           -- 年 
    lease_tm_legth2,          -- 月 
    lease_tm_legth3,          -- 日
    sevly_valut_cd_dsc,
    lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30 as project_price_m, -- 挂牌价格（元/平米/月）
    lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365 as project_price_y, -- 挂牌价格（元/平米/年）   
    case when deal_date is not null then concat(lease_tm_legth,'年',lease_tm_legth2,'个月',lease_tm_legth3,'日') end as publish_date, -- 挂牌租期
    case when deal_date is not null then concat(lease_tm_legth,'年',lease_tm_legth2,'个月',lease_tm_legth3,'日') end as lease_tm, -- 租赁期
    deal_rent_prc as deal_price, -- 成交租金价格
    deal_rent_prc_unit_cmnt as deal_rent_unit, -- 成交租金价格单位说明
    lease_area,                -- 出租面积 
    deal_total_price/(lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365)/lease_area as deal_price_metr_y, -- 成交价格（元/平米/年）
    deal_total_price/(lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30)/lease_area as deal_price_metr_m, -- 成交价格（元/平米/月）
    deal_total_price/(lease_tm_legth*365+lease_tm_legth2*30+lease_tm_legth3)/lease_area as deal_rent_metr_unit, -- 成交租金单价(元/平方米/天)  
    deal_total_price/(lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30) as deal_price_m, -- 成交价格（月）
    deal_total_price/(lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365) as deal_price_y, -- 成交价格（年）
    deal_total_price, -- 成交总价（元）
    lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30 as prj_mon_avg_rent, -- 挂牌月平均租金    
    lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365 as prj_year_avg_rent, --挂牌年平均租金
    lease_tm_legth + lease_tm_legth2 / 12 + lease_tm_legth3 / 365 AS lease_tm_y, -- 总年数 租赁期（年）
    lease_tm_legth * 12 + lease_tm_legth2 + lease_tm_legth3 / 30 AS lease_tm_m, -- 总月数 租赁期（月）   
    lease_tm_legth * 365 + lease_tm_legth2 * 30 + lease_tm_legth3 AS lease_tm_d, -- 总天数 租赁期（天）  
    lsct_dt,    -- 起租日
    due_dt,     -- 到期日
    orgn_aavg_rent_prc,          -- 原年平均租金价格（元/年）
    aavg_y_rent,               -- 年平均租金
    COALESCE(aavg_y_rent,0) - COALESCE(orgn_aavg_rent_prc,0) as deal_year_avg_rent, -- 年平均租金增值
    (COALESCE(aavg_y_rent,0) - COALESCE(orgn_aavg_rent_prc,0))/COALESCE(orgn_aavg_rent_prc,0) as init_cntr_prem_rate, -- 较上份合同溢价率
    cntr_sign_dt,              -- 合同签订日期
    deal_date -- 成交日期
from(select 
    bsn_prj_wrd,      --业务项目关键字
    COALESCE(lease_tm_legth,0)as lease_tm_legth,           -- 年 
    COALESCE(lease_tm_legth2,0)as lease_tm_legth2,          -- 月 
    COALESCE(lease_tm_legth3,0)as lease_tm_legth3,         -- 日
    sevly_valut_cd_dsc, --分别计价代码描述
    deal_rent_prc,            -- 成交租金价格
    deal_rent_prc_unit_cmnt,   -- 成交租金价格单位说明
    lease_area,                -- 出租面积
    deal_rent_tot_prc as deal_total_price,    -- 成交总价（元）
    lsct_dt,    -- 起租日
    due_dt,     -- 到期日
    orgn_aavg_rent_prc,          -- 原年平均租金价格（元/年）
    aavg_y_rent,               -- 年平均租金
    cntr_sign_dt,              -- 合同签订日期
    deal_date -- 成交日期
from dwd.dwd_ast_lease_deal_rec_fct
where dt = '${dmp_day}' and deal_date is not null
)k1),
tzccz_fyjl_fymx as (
select 
    prj,
    SUM(CASE WHEN cc_rl_dsc = '出租方交易服务会员' THEN cc_amt_yuan ELSE 0 END) AS lessor_agent, -- 出租方会员分佣金额
    SUM(CASE WHEN cc_rl_dsc = '承租方交易服务会员' THEN cc_amt_yuan ELSE 0 END) AS lessee_agent, -- 承租方会员分佣金额
    SUM(CASE WHEN cc_rl_dsc = '北交所' THEN cc_amt_yuan ELSE 0 END) AS cbex_fee_amt -- 北交所服务费
from std.std_bjhl_tzccz_fyjl_fymx_d
where dt = '${dmp_day}'
group by prj
),
tzccz_yxczfxx as (
SELECT 
    project_id, -- 项目ID
    CONCAT_WS(',', COLLECT_LIST(CASE WHEN is_prty_rentw = 1 THEN intnt_rent_nm END)) AS prty_rent_names, -- 有优先承租权的原承租方名称
    COUNT(DISTINCT intnt_rent_nm) AS intnt_rent_count, -- 意向承租方数量
    CONCAT_WS(',', COLLECT_LIST(DISTINCT intnt_rent_nm)) AS intnt_rent_list, -- 所有去重后的意向承租方名称
    CONCAT_WS(',', COLLECT_LIST(CASE WHEN final_qlfy_rst_dsc = '获得资格' THEN intnt_rent_nm END)) AS qlfy_rst_name, -- 合格意向承租方
    CONCAT_WS(',', COLLECT_LIST(CASE WHEN fnl_rent_flg = 1 THEN intnt_rent_nm END)) AS zzczf_name, -- 最终承租方
    CONCAT_WS(',', COLLECT_LIST(CASE WHEN deposit_sts_dsc = '已交纳' THEN cast(mbsh_org as STRING) END)) AS mbsh_org_nm -- 合格意向承租方
FROM 
    std.std_bjhl_tzccz_yxczfxx_d
    where dt = '${dmp_day}' and project_id is not null
GROUP BY 
    project_id -- 按项目ID分组
),
tzccz_zcczxm_fw as (
select
    id,
    k1.posit                  ,--坐落位置
    k2.olvl_admn_rgon_nm as prov,
    k3.olvl_admn_rgon_nm as city,
    k5.olvl_admn_rgon_nm as region_county,
    bscrc_dsc,
    bld_area_sqm,
    CASE 
            WHEN COALESCE(estate_wrnt_no, hs_pty_cert_no, acre_cert_no) IS NULL THEN '无'
            ELSE '有'
        END AS bdcqzt, -- 不动产权证（有/无）
    hs_use_crn_sttn_dsc
from std.std_bjhl_tzccz_zcczxm_fw_d k1
left join (select olvl_admn_rgon_cd,olvl_admn_rgon_nm from dim.dim_admn_rgon_info
           where dt = '${dmp_day}' and edw_dt_src_tbl = 'livebos.txzqydm')k2
on k1.prov = k2.olvl_admn_rgon_cd
left join (select olvl_admn_rgon_cd,olvl_admn_rgon_nm from dim.dim_admn_rgon_info
           where dt = '${dmp_day}' and edw_dt_src_tbl = 'livebos.txzqydm')k3
on k1.city = k3.olvl_admn_rgon_cd
left join (select olvl_admn_rgon_cd,olvl_admn_rgon_nm from dim.dim_admn_rgon_info
           where dt = '${dmp_day}' and edw_dt_src_tbl = 'livebos.txzqydm')k5
on k1.region_county = k5.olvl_admn_rgon_cd
where dt = '${dmp_day}'
 ),
tzccz_ywdd as (
select 
    prj, 
    SUM(CASE WHEN py_side_rl_dsc = '出租方' THEN to_acc_amt_yuan ELSE 0 END) AS lessor_txn_serv_fee, -- 出租方交易服务费
    SUM(CASE WHEN py_side_rl_dsc = '承租方' THEN to_acc_amt_yuan ELSE 0 END) AS rent_txn_serv_fee -- 承租方交易服务费
from std.std_bjhl_tzccz_ywdd_d
where dt = '${dmp_day}' and ordr_tp_dsc ='交易服务费' and ordr_sts_dsc = '支付成功' and py_side_rl_dsc in ('出租方','承租方')
group by prj
),
tzccz_yxczfxx_1 as (
SELECT 
    k1.project_id,
    concat_ws(',', collect_set(cast(k2.usr_nm AS STRING))) AS rent_txn_serv_mem -- 承租方受托交易服务会员
FROM (
    SELECT DISTINCT project_id, mbsh_org FROM std.std_bjhl_tzccz_yxczfxx_d WHERE dt = '${dmp_day}' AND deposit_sts_dsc = '已交纳' AND mbsh_org IS NOT NULL
) k1
INNER JOIN (
    SELECT DISTINCT sub_org, usr_nm FROM std.std_bjhl_tuser_d WHERE dt = '${dmp_day}' AND sub_org IS NOT NULL AND usr_nm IS NOT NULL
) k2 ON k1.mbsh_org = k2.sub_org
GROUP BY k1.project_id
),
tbid_fwhydy as (
SELECT 
    k1.org_id,
    concat_ws(',', collect_set(cast(k2.usr_nm AS STRING))) AS lessor_txn_serv_mem -- 出租方受托交易服务会员
FROM (
    SELECT DISTINCT org_id, rltv FROM std.std_bjhl_tbid_fwhydy_d WHERE dt = '${dmp_day}'  -- 服务会员对应关系
) k1
INNER JOIN (
    SELECT DISTINCT id, usr_nm FROM std.std_bjhl_tuser_d WHERE dt = '${dmp_day}'  -- 用户管理
) k2 ON k1.rltv = k2.id
GROUP BY k1.org_id
)
select 
    distinct
    k1.bsn_prj_id, 	 -- 业务项目ID
    k1.project_code, -- 项目编号
    k1.project_name, -- 项目名称
    k2.lessor_tp_dsc, -- 出租方类型  
    k2.ast_src_nm_2, -- 资产来源(2级)    
    k2.ast_src_nm_3, -- 资产来源(3级)   
    k2.ast_src_nm_4, -- 资产来源(4级)
    k2.blng_grp, -- 所属集团 
    k1.lessor_nm, -- 出租方名称
    k1.rent_lst_prc, -- 挂牌价格（元）
    k1.prj_prc_unit_dsc, -- 挂牌价格单位
    k3.sevly_valut_cd_dsc as is_sep_pricing, -- 是否分别计价
    '' as pricing_comp, -- 分别计价构成情况
    k1.rent_lit_tot_prc / k3.project_price_m as project_price_m,  -- 挂牌价格（元/平米/月）
    k1.rent_lit_tot_prc / k3.project_price_y as project_price_y, -- 挂牌价格（元/平米/年）
    k1.rent_lit_tot_prc, -- 挂牌总价(元) 
    k3.publish_date, -- 挂牌租期
    from_unixtime(unix_timestamp(k1.esr_strt_dt, 'yyyyMMdd'), 'yyyy-MM-dd')as esr_strt_dt, -- 披露开始日期
    k1.rnt_fee_est_yuan, -- 租金估价(元)
    from_unixtime(unix_timestamp(k1.esr_end_dt, 'yyyyMMdd'), 'yyyy-MM-dd')as esr_end_dt, -- 披露结束日期
    k3.deal_price, -- 成交租金价格
    k3.deal_rent_unit, -- 成交租金价格单位说明
    k3.deal_price_metr_y, -- 成交价格（元/平米/年）
    k3.deal_price_metr_m, -- 成交价格（元/平米/月）
    k3.deal_rent_metr_unit, -- 成交价格(元/平方米/天)
    k3.deal_price_y, -- 成交价格（年）
    k3.deal_price_m, -- 成交价格（月）
    from_unixtime(unix_timestamp(k3.deal_date, 'yyyyMMdd'), 'yyyy-MM-dd')as deal_date, -- 成交日期
    k3.deal_total_price, -- 成交总价（元）
    k1.prj_sts_dsc, -- 项目状态
    k1.prj_pnp, -- 项目负责人
    k1.prj_blng_dept, -- 项目所属部门
    k1.prj_clm_dept_id, -- 项目认领部门
    k4.gdzt,    -- 归档情况
    k1.pick_mod_dsc, -- 遴选方式
    k1.ntw_bid_tp_dsc, -- 网络竞价类型
    k1.deposit_yuan, -- 保证金金额(元)
    k5.cbex_fee_amt, -- 北交所服务费
    k5.lessor_agent, -- 出租方会员分佣金额
    k5.lessee_agent, -- 承租方会员分佣金额
    k1.ast_cgy_dsc, -- 资产类别
    k1.nbjcqkmc, -- 内部决策情况名称
    k1.project_type_dsc, -- 项目类型
    k1.authorize_unit, -- 批准单位
    k1.est_unit_nm, -- 估价单位名称
    k1.est_file_avl_dt_beg, -- 估价文件有效期起始
    k1.est_file_avl_dt_ct_of, -- 估价文件有效期截止
    case when k1.is_exst_prty_rentw = '1' then '是'
         when k1.is_exst_prty_rentw = '0' then '否'
         else k1.is_exst_prty_rentw end as is_exst_prty_rentw, -- 是否存在优先承租权
    k6.prty_rent_names, -- 有优先承租权的原承租方名称
    k7.prov, -- 坐落位置（省）
    k7.city, -- 坐落位置（市）
    k7.region_county, -- 坐落位置（区）
    k7.bscrc_dsc, -- 商圈
    k7.bld_area_sqm, -- 建筑面积(平方米)
    k7.bdcqzt, -- 不动产权证（有/无）
    k7.hs_use_crn_sttn_dsc, -- 使用现状
    k8.loct_lo,          -- 坐落位置[土地]
    k8.land_area,        -- 土地面积（平方米）
    k8.use,               -- 用途[土地]
    k8.tp,                -- 类型
    k8.use_yrs,          -- 使用年限
    k8.used_yrs,        -- 已用年限
    k2.cty_contri_corp_lead_dept    ,-- 国家出资企业或主管部门
    k2.ast_src_entp_nm_1           ,-- 资产来源（企业1级）       
    k2.ast_src_entp_nm_2           ,-- 资产来源（企业2级）       
    k2.ast_src_entp_nm_3           ,-- 资产来源（企业3级）       
    k2.ast_src_entp_nm_4           ,-- 资产来源（企业4级）
    k2.industry_tp_dsc, -- 所属行业类型描述
    k2.ctc_psn                      ,--联系人                    
    k2.ctc_psn_phone                ,--联系人手机号
    k2.msg_addr                     ,--通讯地址                  
    k2.contact_bank_account_nm      ,--银行账户名称              
    k2.contact_bank_account_acc_no  ,--银行账户账号              
    k2.opn_acc_bnk_nm               ,--开户银行名称              
    k2.opn_acc_bnk_subbr            ,--开户银行支行              
    k2.dep_bnk_bnk_cd,                --开户行联行号 
    k1.rnt_fee_prj_prc_rmrk, -- 租金挂牌价备注
    k1.pcolt_lhder_cnt, -- 拟征集承租方个数
    k1.use_rqmt_cd_dsc, -- 房屋使用用途
    k1.rent_postulate, -- 承租方资格条件
    k1.rent_lit_tot_prc / k3.prj_mon_avg_rent as prj_mon_avg_rent, -- 挂牌月平均租金    
    k1.rent_lit_tot_prc / k3.prj_year_avg_rent as prj_year_avg_rent, --挂牌年平均租金
    k6.intnt_rent_count, -- 意向承租方数量
    k6.intnt_rent_list, -- 意向承租方报名情况
    k6.qlfy_rst_name, -- 合格意向承租方
    k6.zzczf_name, -- 最终承租方
    k3.lsct_dt,    -- 起租日
    k3.due_dt,     -- 到期日
    k3.lease_tm, -- 租赁期
    k3.lease_tm_y, -- 总年数 租赁期（年）
    k3.lease_tm_m, -- 总月数 租赁期（月）   
    k3.lease_tm_d, -- 总天数 租赁期（天）    
    k3.orgn_aavg_rent_prc,          -- 原年平均租金价格（元/年）
    k3.aavg_y_rent,               -- 年平均租金
    k9.lessor_txn_serv_fee, -- 出租方交易服务费
    k9.rent_txn_serv_fee, -- 承租方交易服务费
    k10.rent_txn_serv_mem, -- 承租方受托交易服务会员
    k11.lessor_txn_serv_mem, -- 出租方受托交易服务会员
    k1.deal_mth_cd_dsc, -- 成交方式
    substr(k3.cntr_sign_dt,1,10)cntr_sign_dt,              -- 合同签订日期
    '' as vchr_date, -- 凭证出具时间
    case when k3.deal_total_price - k1.rent_lit_tot_prc < 0 then null
    	 else k3.deal_total_price - k1.rent_lit_tot_prc end as total_price_incre, -- 总价增值
    k3.deal_total_price - k1.rnt_fee_est_yuan as evalu_incre, -- 评估增值
    k3.deal_year_avg_rent, -- 年平均租金增值
    case when k3.init_cntr_prem_rate < 0 then null else k3.init_cntr_prem_rate end init_cntr_prem_rate, -- 较上份合同溢价率
    (k3.deal_total_price - k1.rnt_fee_est_yuan)/ k1.rnt_fee_est_yuan as listg_prem_rate, -- 挂牌溢价率   
    case when (k3.deal_total_price - k1.rent_lit_tot_prc)/ k1.rent_lit_tot_prc < 0 then null 
    	 else (k3.deal_total_price - k1.rent_lit_tot_prc)/ k1.rent_lit_tot_prc end as bid_prem_rate, --竞价溢价率   
    case when ((k3.deal_total_price - k1.rent_lit_tot_prc)/ k1.rent_lit_tot_prc < 0) 
    	 and (k3.lease_area - k1.plan_lease_area < 0) then '成交面积<最后一次挂牌面积' end as prem_desc, -- 溢价情况为空说明
    k1.plan_lease_area, -- 拟出租面积(平方米)
    k3.lease_area,                -- 出租面积
    0 as addval_rate, -- 增值率
    '' as ep_cost,   -- 减免费用
    k1.is_nal_hs_rent, -- 是否国有房屋出租
    k2.oasset_reg_org_dsc,--国资监管机构描述
    k2.custd_org_depdc_prov, --监管机构属地(省)
    k7.posit              	 -- 坐落位置（房屋）
    
    
    
    
from tzccz_zcczxm k1
left join tzccz_czfxx k2
on k1.bsn_prj_id = k2.project_id
left join tzccz_cjjl k3
on k1.bsn_prj_id = k3.bsn_prj_wrd 
left join (select distinct xmbh,gdzt
           from dwd.dwd_tgd_jl_d
           where dt = '${dmp_day}')k4
on k1.project_code = k4.xmbh
left join tzccz_fyjl_fymx k5
on k1.bsn_prj_id = k5.prj
left join tzccz_yxczfxx k6
on k1.bsn_prj_id = k6.project_id
left join tzccz_zcczxm_fw k7
on k1.bsn_prj_id = k7.id
left join std.std_bjhl_tzccz_zcczxm_td_d k8
on k1.bsn_prj_id = k8.land_id
left join tzccz_ywdd k9
on k1.bsn_prj_id = k9.prj
left join tzccz_yxczfxx_1 k10
on k1.bsn_prj_id = k10.project_id
left join tbid_fwhydy k11
on k1.mbsh_org = k11.org_id